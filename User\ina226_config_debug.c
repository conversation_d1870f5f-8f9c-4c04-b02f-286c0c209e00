/**
 ******************************************************************************
 * @file    ina226_config_debug.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   INA226 configuration debugging utilities
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "ina226_config_debug.h"
#include "mod_ina226.h"
#include "app_uart.h"
#include "app_util.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
/* Public functions ----------------------------------------------------------*/

/**
 * @brief Detailed INA226 register analysis
 * @retval None
 */
void INA226_AnalyzeRegisters(void)
{
    uint16_t reg_value;
    
    logi("=== INA226 Register Analysis ===\r\n");
    
    // Initialize basic INA226
    if (!INA226_Init()) {
        logi("ERROR: INA226 init failed\r\n");
        return;
    }
    
    // Read and analyze Config Register (0x00)
    if (INA226_ReadRegister(0x00, &reg_value)) {
        logi("Config Register (0x00): 0x%04X\r\n", reg_value);
        
        uint16_t mode = reg_value & 0x0007;
        uint16_t vshct = (reg_value >> 3) & 0x0007;
        uint16_t vbusct = (reg_value >> 6) & 0x0007;
        uint16_t avg = (reg_value >> 9) & 0x0007;
        
        logi("  Mode (bits 2-0): 0x%X = ", mode);
        switch(mode) {
            case 0: logi("Power-down\r\n"); break;
            case 1: logi("Shunt voltage, triggered\r\n"); break;
            case 2: logi("Bus voltage, triggered\r\n"); break;
            case 3: logi("Shunt and bus, triggered\r\n"); break;
            case 4: logi("ADC off (power-down)\r\n"); break;
            case 5: logi("Shunt voltage, continuous\r\n"); break;
            case 6: logi("Bus voltage, continuous\r\n"); break;
            case 7: logi("Shunt and bus, continuous\r\n"); break;
        }
        
        logi("  VSHCT (bits 5-3): 0x%X = ", vshct);
        switch(vshct) {
            case 0: logi("140µs\r\n"); break;
            case 1: logi("204µs\r\n"); break;
            case 2: logi("332µs\r\n"); break;
            case 3: logi("588µs\r\n"); break;
            case 4: logi("1.1ms\r\n"); break;
            case 5: logi("2.116ms\r\n"); break;
            case 6: logi("4.156ms\r\n"); break;
            case 7: logi("8.244ms\r\n"); break;
        }
        
        logi("  VBUSCT (bits 8-6): 0x%X = ", vbusct);
        switch(vbusct) {
            case 0: logi("140µs\r\n"); break;
            case 1: logi("204µs\r\n"); break;
            case 2: logi("332µs\r\n"); break;
            case 3: logi("588µs\r\n"); break;
            case 4: logi("1.1ms\r\n"); break;
            case 5: logi("2.116ms\r\n"); break;
            case 6: logi("4.156ms\r\n"); break;
            case 7: logi("8.244ms\r\n"); break;
        }
        
        logi("  AVG (bits 11-9): 0x%X = ", avg);
        switch(avg) {
            case 0: logi("1 sample\r\n"); break;
            case 1: logi("4 samples\r\n"); break;
            case 2: logi("16 samples\r\n"); break;
            case 3: logi("64 samples\r\n"); break;
            case 4: logi("128 samples\r\n"); break;
            case 5: logi("256 samples\r\n"); break;
            case 6: logi("512 samples\r\n"); break;
            case 7: logi("1024 samples\r\n"); break;
        }
        
        // Calculate conversion time
        uint16_t conv_times[] = {140, 204, 332, 588, 1100, 2116, 4156, 8244};
        uint16_t avg_counts[] = {1, 4, 16, 64, 128, 256, 512, 1024};
        
        uint32_t total_time_us = (conv_times[vshct] + conv_times[vbusct]) * avg_counts[avg];
        float conversion_rate = 1000000.0f / total_time_us;
        
        logi("  Calculated conversion time: %lu µs\r\n", total_time_us);
        logi("  Theoretical conversion rate: %.1f Hz\r\n", conversion_rate);
    }
    
    // Read Mask/Enable Register (0x06)
    if (INA226_ReadRegister(0x06, &reg_value)) {
        logi("Mask/Enable Register (0x06): 0x%04X\r\n", reg_value);
        logi("  SOL (bit 15): %s\r\n", (reg_value & 0x8000) ? "Shunt Over-Limit" : "Normal");
        logi("  SUL (bit 14): %s\r\n", (reg_value & 0x4000) ? "Shunt Under-Limit" : "Normal");
        logi("  BOL (bit 13): %s\r\n", (reg_value & 0x2000) ? "Bus Over-Limit" : "Normal");
        logi("  BUL (bit 12): %s\r\n", (reg_value & 0x1000) ? "Bus Under-Limit" : "Normal");
        logi("  POL (bit 11): %s\r\n", (reg_value & 0x0800) ? "Power Over-Limit" : "Normal");
        logi("  CNVR (bit 10): %s\r\n", (reg_value & 0x0400) ? "ENABLED" : "DISABLED");
        logi("  AFF (bit 4): %s\r\n", (reg_value & 0x0010) ? "Alert Function Flag" : "Normal");
        logi("  CVRF (bit 3): %s\r\n", (reg_value & 0x0008) ? "Conversion Ready" : "Not Ready");
        logi("  OVF (bit 2): %s\r\n", (reg_value & 0x0004) ? "Math Overflow" : "Normal");
        logi("  APOL (bit 1): %s\r\n", (reg_value & 0x0002) ? "Alert Polarity Inverted" : "Normal");
        logi("  LEN (bit 0): %s\r\n", (reg_value & 0x0001) ? "Latch Enabled" : "Transparent");
    }
    
    // Read Alert Limit Register (0x07)
    if (INA226_ReadRegister(0x07, &reg_value)) {
        logi("Alert Limit Register (0x07): 0x%04X\r\n", reg_value);
    }
    
    // Read Manufacturer ID (0xFE)
    if (INA226_ReadRegister(0xFE, &reg_value)) {
        logi("Manufacturer ID (0xFE): 0x%04X ", reg_value);
        if (reg_value == 0x5449) {
            logi("(CORRECT - Texas Instruments)\r\n");
        } else {
            logi("(ERROR - Should be 0x5449)\r\n");
        }
    }
    
    // Read Die ID (0xFF)
    if (INA226_ReadRegister(0xFF, &reg_value)) {
        logi("Die ID (0xFF): 0x%04X ", reg_value);
        if (reg_value == 0x2260) {
            logi("(CORRECT - INA226)\r\n");
        } else {
            logi("(ERROR - Should be 0x2260)\r\n");
        }
    }
}

/**
 * @brief Test different INA226 configurations for ALERT
 * @retval None
 */
void INA226_TestAlertConfigurations(void)
{
    uint16_t reg_value;
    
    logi("=== INA226 ALERT Configuration Test ===\r\n");
    
    if (!INA226_Init()) {
        logi("ERROR: INA226 init failed\r\n");
        return;
    }
    
    // Test 1: Fast continuous mode with CNVR alert
    logi("Test 1: Fast continuous mode with CNVR alert\r\n");
    
    // Set to fastest conversion: AVG=1, VBUSCT=140µs, VSHCT=140µs, Mode=Continuous
    uint16_t fast_config = 0x4000 | (0 << 9) | (0 << 6) | (0 << 3) | 7; // 0x4007
    if (INA226_WriteRegister(0x00, fast_config)) {
        logi("  Config set to fast mode: 0x%04X\r\n", fast_config);
    }
    
    // Enable CNVR alert
    if (INA226_WriteRegister(0x06, 0x0400)) {
        logi("  CNVR alert enabled\r\n");
    }
    
    // Monitor for conversion ready flags
    logi("  Monitoring CVRF for 5 seconds...\r\n");
    uint32_t cvrf_count = 0;
    
    for (int i = 0; i < 50; i++) { // 5 seconds, 100ms intervals
        if (INA226_ReadRegister(0x06, &reg_value)) {
            if (reg_value & 0x0008) { // CVRF bit
                cvrf_count++;
                if (cvrf_count <= 10) {
                    logi("    CVRF detected at %d.%d sec (count: %lu)\r\n", 
                         i/10, i%10, cvrf_count);
                }
            }
        }
        s_delay_ms(100);
    }
    
    logi("  CVRF count in 5 seconds: %lu\r\n", cvrf_count);
    logi("  Estimated rate: %.1f Hz\r\n", (float)cvrf_count / 5.0f);
    
    if (cvrf_count > 0) {
        logi("  RESULT: INA226 is converting and setting CVRF flag\r\n");
    } else {
        logi("  PROBLEM: No CVRF flags detected - INA226 may not be converting\r\n");
    }
    
    // Test 2: Check if ALERT pin follows CVRF
    logi("Test 2: Check ALERT pin behavior\r\n");
    
    // Read current pin state
    uint8_t pin_state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_8);
    logi("  Current PB8 state: %d\r\n", pin_state);
    
    // Monitor pin state changes
    logi("  Monitoring PB8 changes for 5 seconds...\r\n");
    uint8_t prev_state = pin_state;
    uint32_t change_count = 0;
    
    for (int i = 0; i < 50; i++) {
        uint8_t current_state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_8);
        if (current_state != prev_state) {
            change_count++;
            logi("    PB8 change %lu at %d.%d sec: %d -> %d\r\n", 
                 change_count, i/10, i%10, prev_state, current_state);
        }
        prev_state = current_state;
        s_delay_ms(100);
    }
    
    logi("  PB8 changes in 5 seconds: %lu\r\n", change_count);
    
    if (change_count > 0) {
        logi("  RESULT: ALERT pin is active\r\n");
    } else {
        logi("  PROBLEM: ALERT pin shows no activity\r\n");
        logi("  Check:\r\n");
        logi("    1. ALERT pin connection to PB8\r\n");
        logi("    2. INA226 power supply\r\n");
        logi("    3. ALERT pin polarity (should be active low)\r\n");
    }
}

/**
 * @brief Force INA226 conversion and check ALERT
 * @retval None
 */
void INA226_ForceConversionTest(void)
{
    uint16_t reg_value;
    
    logi("=== Force Conversion Test ===\r\n");
    
    if (!INA226_Init()) {
        logi("ERROR: INA226 init failed\r\n");
        return;
    }
    
    // Set to triggered mode first
    logi("Setting to triggered mode...\r\n");
    uint16_t triggered_config = 0x4000 | (0 << 9) | (0 << 6) | (0 << 3) | 3; // Mode = 3 (triggered)
    if (INA226_WriteRegister(0x00, triggered_config)) {
        logi("Config set to triggered mode: 0x%04X\r\n", triggered_config);
    }
    
    // Enable CNVR alert
    if (INA226_WriteRegister(0x06, 0x0400)) {
        logi("CNVR alert enabled\r\n");
    }
    
    // Force 10 conversions manually
    for (int i = 0; i < 10; i++) {
        logi("Forcing conversion %d...\r\n", i + 1);
        
        // Clear CVRF by reading it
        INA226_ReadRegister(0x06, &reg_value);
        
        // Trigger conversion by writing to config register
        INA226_WriteRegister(0x00, triggered_config);
        
        // Wait for conversion to complete
        uint32_t timeout = 100; // 1 second timeout
        bool conversion_ready = false;
        
        while (timeout-- > 0) {
            if (INA226_ReadRegister(0x06, &reg_value)) {
                if (reg_value & 0x0008) { // CVRF bit
                    conversion_ready = true;
                    logi("  Conversion ready after %lu ms\r\n", 100 - timeout);
                    break;
                }
            }
            s_delay_ms(10);
        }
        
        if (conversion_ready) {
            // Read the actual values
            uint16_t shunt_raw, bus_raw;
            if (INA226_ReadRegister(0x01, &shunt_raw) && INA226_ReadRegister(0x02, &bus_raw)) {
                int16_t shunt_signed = (int16_t)shunt_raw;
                float shunt_mv = shunt_signed * 0.0025f;
                float bus_v = bus_raw * 0.00125f;
                float current_ma = shunt_mv / 0.75f;
                
                logi("  Shunt: 0x%04X (%.2fmV, %.2fmA), Bus: 0x%04X (%.2fV)\r\n",
                     shunt_raw, shunt_mv, current_ma, bus_raw, bus_v);
            }
            
            // Check ALERT pin state
            uint8_t pin_state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_8);
            logi("  ALERT pin state: %d\r\n", pin_state);
        } else {
            logi("  Conversion timeout!\r\n");
        }
        
        s_delay_ms(500);
    }
}

/**
 * @brief Run complete INA226 configuration diagnosis
 * @retval None
 */
void INA226_CompleteConfigDiagnosis(void)
{
    logi("=== INA226 Complete Configuration Diagnosis ===\r\n");
    
    // Test 1: Register analysis
    INA226_AnalyzeRegisters();
    s_delay_ms(2000);
    
    // Test 2: ALERT configuration test
    INA226_TestAlertConfigurations();
    s_delay_ms(2000);
    
    // Test 3: Force conversion test
    INA226_ForceConversionTest();
    
    logi("=== Configuration Diagnosis Complete ===\r\n");
}
