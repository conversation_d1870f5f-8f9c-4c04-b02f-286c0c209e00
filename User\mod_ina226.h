/**
 ******************************************************************************
 * @file    mod_ina226.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-02
 * @brief   INA226 power monitor module header file
 ******************************************************************************
 */

#ifndef __MOD_INA226_H
#define __MOD_INA226_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

// Forward declaration for HAL types
typedef struct __I2C_HandleTypeDef I2C_HandleTypeDef;

/* Exported types ------------------------------------------------------------*/

/**
 * @brief INA226 measurement data structure
 */
typedef struct {
    float voltage_V;        // Bus voltage in volts
    float current_A;        // Current in amperes
    float power_W;          // Power in watts
    float shunt_voltage_mV; // Shunt voltage in millivolts
} INA226_Data_t;

/**
 * @brief INA226 configuration structure
 */
typedef struct {
    uint16_t avg_mode;      // Averaging mode
    uint16_t bus_conv_time; // Bus voltage conversion time
    uint16_t shunt_conv_time; // Shunt voltage conversion time
    uint16_t mode;          // Operating mode
} INA226_Config_t;

/* Exported constants --------------------------------------------------------*/

// INA226 I2C Address (A1=0, A0=0)
#define INA226_I2C_ADDR         0x80    // 7-bit address: 0x40, shifted left for HAL

// INA226 Register Addresses
#define INA226_REG_CONFIG       0x00    // Configuration Register
#define INA226_REG_SHUNT_V      0x01    // Shunt Voltage Register
#define INA226_REG_BUS_V        0x02    // Bus Voltage Register
#define INA226_REG_POWER        0x03    // Power Register
#define INA226_REG_CURRENT      0x04    // Current Register
#define INA226_REG_CALIBRATION  0x05    // Calibration Register
#define INA226_REG_MASK_ENABLE  0x06    // Mask/Enable Register
#define INA226_REG_ALERT_LIMIT  0x07    // Alert Limit Register
#define INA226_REG_MANUF_ID     0xFE    // Manufacturer ID Register
#define INA226_REG_DIE_ID       0xFF    // Die ID Register

// Configuration Register Bits
#define INA226_CONFIG_RESET     0x8000  // Reset bit
#define INA226_CONFIG_AVG_1     0x0000  // 1 sample average
#define INA226_CONFIG_AVG_4     0x0200  // 4 sample average
#define INA226_CONFIG_AVG_16    0x0400  // 16 sample average
#define INA226_CONFIG_AVG_64    0x0600  // 64 sample average
#define INA226_CONFIG_AVG_128   0x0800  // 128 sample average
#define INA226_CONFIG_AVG_256   0x0A00  // 256 sample average
#define INA226_CONFIG_AVG_512   0x0C00  // 512 sample average
#define INA226_CONFIG_AVG_1024  0x0E00  // 1024 sample average

#define INA226_CONFIG_VBUSCT_140US  0x0000  // 140us conversion time
#define INA226_CONFIG_VBUSCT_204US  0x0040  // 204us conversion time
#define INA226_CONFIG_VBUSCT_332US  0x0080  // 332us conversion time
#define INA226_CONFIG_VBUSCT_588US  0x00C0  // 588us conversion time
#define INA226_CONFIG_VBUSCT_1100US 0x0100  // 1.1ms conversion time
#define INA226_CONFIG_VBUSCT_2116US 0x0140  // 2.116ms conversion time
#define INA226_CONFIG_VBUSCT_4156US 0x0180  // 4.156ms conversion time
#define INA226_CONFIG_VBUSCT_8244US 0x01C0  // 8.244ms conversion time

#define INA226_CONFIG_VSHCT_140US   0x0000  // 140us conversion time
#define INA226_CONFIG_VSHCT_204US   0x0008  // 204us conversion time
#define INA226_CONFIG_VSHCT_332US   0x0010  // 332us conversion time
#define INA226_CONFIG_VSHCT_588US   0x0018  // 588us conversion time
#define INA226_CONFIG_VSHCT_1100US  0x0020  // 1.1ms conversion time
#define INA226_CONFIG_VSHCT_2116US  0x0028  // 2.116ms conversion time
#define INA226_CONFIG_VSHCT_4156US  0x0030  // 4.156ms conversion time
#define INA226_CONFIG_VSHCT_8244US  0x0038  // 8.244ms conversion time

#define INA226_CONFIG_MODE_POWER_DOWN   0x0000  // Power down
#define INA226_CONFIG_MODE_SHUNT_TRIG   0x0001  // Shunt voltage triggered
#define INA226_CONFIG_MODE_BUS_TRIG     0x0002  // Bus voltage triggered
#define INA226_CONFIG_MODE_SHUNT_BUS_TRIG 0x0003 // Shunt and bus triggered
#define INA226_CONFIG_MODE_POWER_DOWN2  0x0004  // Power down
#define INA226_CONFIG_MODE_SHUNT_CONT   0x0005  // Shunt voltage continuous
#define INA226_CONFIG_MODE_BUS_CONT     0x0006  // Bus voltage continuous
#define INA226_CONFIG_MODE_SHUNT_BUS_CONT 0x0007 // Shunt and bus continuous

// Default configuration
#define INA226_CONFIG_DEFAULT   (INA226_CONFIG_AVG_1 | \
                                INA226_CONFIG_VBUSCT_1100US | \
                                INA226_CONFIG_VSHCT_1100US | \
                                INA226_CONFIG_MODE_SHUNT_BUS_CONT)

// Calibration constants
#define INA226_SHUNT_RESISTANCE_OHMS    0.75f   // 750mΩ shunt resistor (R750)
#define INA226_CURRENT_LSB_A            0.0001f // 100µA per bit (adjusted for 0.75Ω)
#define INA226_POWER_LSB_W              (INA226_CURRENT_LSB_A * 25.0f) // 25 * Current_LSB

// I2C GPIO pins
#define INA226_I2C                  I2C1
#define INA226_I2C_CLK              RCC_APB1Periph_I2C1
#define INA226_I2C_SCL_PIN          GPIO_Pin_6
#define INA226_I2C_SCL_GPIO_PORT    GPIOB
#define INA226_I2C_SCL_GPIO_CLK     RCC_AHB1Periph_GPIOB
#define INA226_I2C_SCL_SOURCE       GPIO_PinSource6
#define INA226_I2C_SCL_AF           GPIO_AF_I2C1

#define INA226_I2C_SDA_PIN          GPIO_Pin_7
#define INA226_I2C_SDA_GPIO_PORT    GPIOB
#define INA226_I2C_SDA_GPIO_CLK     RCC_AHB1Periph_GPIOB
#define INA226_I2C_SDA_SOURCE       GPIO_PinSource7
#define INA226_I2C_SDA_AF           GPIO_AF_I2C1

// INA226 ALERT pin for EXTI interrupt
#define INA226_ALERT_PIN            GPIO_Pin_8
#define INA226_ALERT_GPIO_PORT      GPIOB
#define INA226_ALERT_GPIO_CLK       RCC_AHB1Periph_GPIOA
#define INA226_ALERT_EXTI_LINE      EXTI_Line8
#define INA226_ALERT_EXTI_PORT      EXTI_PortSourceGPIOA
#define INA226_ALERT_EXTI_PIN       EXTI_PinSource8
#define INA226_ALERT_IRQn           EXTI9_5_IRQn

// DMA configuration for I2C1
#define INA226_DMA                  DMA1
#define INA226_DMA_CLK              RCC_AHB1Periph_DMA1
#define INA226_DMA_STREAM           DMA1_Stream0
#define INA226_DMA_CHANNEL          DMA_Channel_1
#define INA226_DMA_IRQn             DMA1_Stream0_IRQn

// Ping-pong buffer configuration
#define INA226_BUFFER_SIZE          100
#define INA226_BUFFER_COUNT         2

/* Exported macro ------------------------------------------------------------*/

/* Exported functions --------------------------------------------------------*/

/**
 * @brief Initialize INA226 module
 * @retval true if initialization successful, false otherwise
 */
bool INA226_Init(void);

/**
 * @brief Read all measurement data from INA226
 * @param data: Pointer to data structure to store results
 * @retval true if read successful, false otherwise
 */
bool INA226_ReadData(INA226_Data_t *data);

/**
 * @brief Read bus voltage from INA226
 * @retval Bus voltage in volts, or -1.0f on error
 */
float INA226_ReadBusVoltage(void);

/**
 * @brief Read shunt voltage from INA226
 * @retval Shunt voltage in millivolts, or -1.0f on error
 */
float INA226_ReadShuntVoltage(void);

/**
 * @brief Read current from INA226
 * @retval Current in amperes, or -1.0f on error
 */
float INA226_ReadCurrent(void);

/**
 * @brief Read power from INA226
 * @retval Power in watts, or -1.0f on error
 */
float INA226_ReadPower(void);

/**
 * @brief Configure INA226 settings
 * @param config: Pointer to configuration structure
 * @retval true if configuration successful, false otherwise
 */
bool INA226_Configure(const INA226_Config_t *config);

/**
 * @brief Reset INA226 to default settings
 * @retval true if reset successful, false otherwise
 */
bool INA226_Reset(void);

/**
 * @brief Check if INA226 is present and responding
 * @retval true if device is present, false otherwise
 */
bool INA226_IsPresent(void);

/**
 * @brief Test INA226 functionality
 * @retval true if all tests pass, false otherwise
 */
bool INA226_Test(void);

/**
 * @brief Print INA226 configuration information
 * @retval None
 */
void INA226_PrintConfig(void);

/**
 * @brief Initialize INA226 with EXTI+DMA optimization
 * @retval true if initialization successful, false otherwise
 */
bool INA226_InitOptimized(void);

/**
 * @brief Start optimized data acquisition using EXTI+DMA
 * @retval true if start successful, false otherwise
 */
bool INA226_StartOptimizedAcquisition(void);

/**
 * @brief Stop optimized data acquisition
 * @retval None
 */
void INA226_StopOptimizedAcquisition(void);

/**
 * @brief Get latest shunt voltage data from ping-pong buffer
 * @param buffer: Pointer to buffer to store data
 * @param count: Number of samples to read (max INA226_BUFFER_SIZE)
 * @retval Number of samples actually read
 */
uint32_t INA226_GetLatestData(uint16_t *buffer, uint32_t count);

/**
 * @brief Check if new data is available in ping-pong buffer
 * @retval true if new data available, false otherwise
 */
bool INA226_IsNewDataAvailable(void);

/**
 * @brief GPIO EXTI callback for INA226 ALERT pin
 * @param GPIO_Pin: GPIO pin that triggered the interrupt
 * @retval None
 */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin);

/**
 * @brief I2C memory reception complete callback
 * @param hi2c: I2C handle pointer (can be NULL in optimized mode)
 * @retval None
 */
void HAL_I2C_MemRxCpltCallback(I2C_HandleTypeDef *hi2c);

#ifdef __cplusplus
}
#endif

#endif /* __MOD_INA226_H */
