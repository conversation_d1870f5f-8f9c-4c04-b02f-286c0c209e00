/**
 ******************************************************************************
 * @file    ina226_optimized_example.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   INA226 optimized EXTI+DMA example header file
 ******************************************************************************
 */

#ifndef __INA226_OPTIMIZED_EXAMPLE_H
#define __INA226_OPTIMIZED_EXAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/**
 * @brief Example 1: Basic optimized INA226 usage
 * @retval None
 */
void INA226_OptimizedExample_Basic(void);

/**
 * @brief Example 2: High-frequency monitoring with statistics
 * @retval None
 */
void INA226_OptimizedExample_HighFrequency(void);

/**
 * @brief Example 3: Power peak detection
 * @retval None
 */
void INA226_OptimizedExample_PeakDetection(void);

#ifdef __cplusplus
}
#endif

#endif /* __INA226_OPTIMIZED_EXAMPLE_H */
