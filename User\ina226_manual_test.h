/**
 ******************************************************************************
 * @file    ina226_manual_test.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   Manual test header for INA226 EXTI debugging
 ******************************************************************************
 */

#ifndef __INA226_MANUAL_TEST_H
#define __INA226_MANUAL_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/**
 * @brief Very simple manual trigger test
 * @retval None
 */
void INA226_VerySimpleTest(void);

/**
 * @brief Test with continuous monitoring
 * @retval None
 */
void INA226_ContinuousMonitorTest(void);

/**
 * @brief Test raw register reading
 * @retval None
 */
void INA226_RawRegisterTest(void);

/**
 * @brief Run basic manual tests
 * @retval None
 */
void INA226_RunManualTests(void);

#ifdef __cplusplus
}
#endif

#endif /* __INA226_MANUAL_TEST_H */
