/**
 ******************************************************************************
 * @file    ina226_integration_optimized.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   INA226 optimized integration with UI demo
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "ina226_integration_optimized.h"
#include "mod_ina226.h"
#include "ui_demo.h"
#include "app_uart.h"
#include "app_util.h"
#include "FreeRTOS.h"
#include "task.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define SAMPLE_BUFFER_SIZE          100
#define INTEGRATION_TASK_STACK_SIZE 512
#define INTEGRATION_TASK_PRIORITY   3

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static TaskHandle_t integration_task_handle = NULL;
static uint16_t sample_buffer[SAMPLE_BUFFER_SIZE];
static bool integration_running = false;

/* Private function prototypes -----------------------------------------------*/
static void INA226_IntegrationTask(void *pvParameters);
static void ProcessOptimizedData(uint16_t *data, uint32_t count);
static float ConvertToShuntVoltage(uint16_t raw_value);
static float ConvertToCurrent(float shunt_mv);
static float ConvertToPower(float current_a, float voltage_v);

/* Public functions ----------------------------------------------------------*/

/**
 * @brief Initialize optimized INA226 integration
 * @retval true if initialization successful, false otherwise
 */
bool INA226_IntegrationOptimized_Init(void)
{
    logi("=== INA226 Optimized Integration Init ===\r\n");
    
    // Initialize INA226 in optimized mode
    if (!INA226_InitOptimized()) {
        logi("ERROR: INA226 optimized initialization failed!\r\n");
        return false;
    }
    
    // Initialize UI demo
    ui_demo_init();
    ui_create_ina226_display();
    
    // Create integration task
    if (xTaskCreate(INA226_IntegrationTask, 
                    "INA226_Opt", 
                    INTEGRATION_TASK_STACK_SIZE, 
                    NULL, 
                    INTEGRATION_TASK_PRIORITY, 
                    &integration_task_handle) != pdPASS) {
        logi("ERROR: Failed to create INA226 integration task!\r\n");
        return false;
    }
    
    logi("INA226 optimized integration initialized successfully.\r\n");
    return true;
}

/**
 * @brief Start optimized INA226 integration
 * @retval true if start successful, false otherwise
 */
bool INA226_IntegrationOptimized_Start(void)
{
    if (integration_task_handle == NULL) {
        logi("ERROR: Integration not initialized!\r\n");
        return false;
    }
    
    // Start optimized data acquisition
    if (!INA226_StartOptimizedAcquisition()) {
        logi("ERROR: Failed to start optimized acquisition!\r\n");
        return false;
    }
    
    integration_running = true;
    logi("INA226 optimized integration started.\r\n");
    return true;
}

/**
 * @brief Stop optimized INA226 integration
 * @retval None
 */
void INA226_IntegrationOptimized_Stop(void)
{
    integration_running = false;
    INA226_StopOptimizedAcquisition();
    logi("INA226 optimized integration stopped.\r\n");
}

/**
 * @brief Get integration status
 * @retval true if integration is running, false otherwise
 */
bool INA226_IntegrationOptimized_IsRunning(void)
{
    return integration_running;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief FreeRTOS task for optimized INA226 integration
 * @param pvParameters: Task parameters (unused)
 * @retval None
 */
static void INA226_IntegrationTask(void *pvParameters)
{
    uint32_t cycle_count = 0;
    uint32_t total_samples = 0;
    float last_voltage = 0.0f;
    float last_current = 0.0f;
    float last_power = 0.0f;
    float last_shunt_voltage = 0.0f;
    
    logi("INA226 optimized integration task started.\r\n");
    
    while (1) {
        if (integration_running) {
            // Check for new data
            if (INA226_IsNewDataAvailable()) {
                uint32_t samples_read = INA226_GetLatestData(sample_buffer, SAMPLE_BUFFER_SIZE);
                
                if (samples_read > 0) {
                    total_samples += samples_read;
                    
                    // Process the data
                    ProcessOptimizedData(sample_buffer, samples_read);
                    
                    // Calculate average values from the latest batch
                    float voltage_sum = 0.0f;
                    float current_sum = 0.0f;
                    float power_sum = 0.0f;
                    float shunt_sum = 0.0f;
                    
                    for (uint32_t i = 0; i < samples_read; i++) {
                        float shunt_mv = ConvertToShuntVoltage(sample_buffer[i]);
                        float current_a = ConvertToCurrent(shunt_mv);
                        
                        // For this example, assume bus voltage is 3.3V
                        // In real application, you would read this from INA226
                        float voltage_v = 3.3f;
                        float power_w = ConvertToPower(current_a, voltage_v);
                        
                        voltage_sum += voltage_v;
                        current_sum += current_a;
                        power_sum += power_w;
                        shunt_sum += shunt_mv;
                    }
                    
                    // Calculate averages
                    last_voltage = voltage_sum / samples_read;
                    last_current = current_sum / samples_read;
                    last_power = power_sum / samples_read;
                    last_shunt_voltage = shunt_sum / samples_read;
                    
                    // Update UI with latest values
                    ui_update_ina226_data(last_voltage, last_current, last_power, last_shunt_voltage);
                    
                    cycle_count++;
                    
                    // Log statistics every 50 cycles
                    if ((cycle_count % 50) == 0) {
                        logi("Cycle %lu: %lu samples, Avg I=%.6fA, P=%.3fW\r\n", 
                             cycle_count, total_samples, last_current, last_power);
                    }
                }
            }
            
            // Task delay - check for new data every 10ms
            vTaskDelay(pdMS_TO_TICKS(10));
        } else {
            // Integration stopped, wait longer
            vTaskDelay(pdMS_TO_TICKS(100));
        }
    }
}

/**
 * @brief Process optimized data and perform analysis
 * @param data: Pointer to raw shunt voltage data
 * @param count: Number of samples
 * @retval None
 */
static void ProcessOptimizedData(uint16_t *data, uint32_t count)
{
    if (data == NULL || count == 0) return;
    
    // Find min, max, and calculate statistics
    float min_current = 999.0f;
    float max_current = -999.0f;
    uint32_t zero_crossings = 0;
    float prev_current = 0.0f;
    
    for (uint32_t i = 0; i < count; i++) {
        float shunt_mv = ConvertToShuntVoltage(data[i]);
        float current_a = ConvertToCurrent(shunt_mv);
        
        // Update min/max
        if (current_a < min_current) min_current = current_a;
        if (current_a > max_current) max_current = current_a;
        
        // Count zero crossings (sign changes)
        if (i > 0 && ((prev_current >= 0 && current_a < 0) || (prev_current < 0 && current_a >= 0))) {
            zero_crossings++;
        }
        prev_current = current_a;
    }
    
    // Log detailed statistics occasionally
    static uint32_t log_counter = 0;
    if ((++log_counter % 100) == 0) {
        logi("Stats: Min=%.6fA, Max=%.6fA, Range=%.6fA, ZeroCross=%lu\r\n",
             min_current, max_current, max_current - min_current, zero_crossings);
    }
}

/**
 * @brief Convert raw value to shunt voltage
 * @param raw_value: Raw 16-bit value from INA226
 * @retval Shunt voltage in millivolts
 */
static float ConvertToShuntVoltage(uint16_t raw_value)
{
    // INA226 shunt voltage LSB is 2.5µV per bit
    int16_t signed_value = (int16_t)raw_value;
    return signed_value * 0.0025f; // Convert to mV
}

/**
 * @brief Convert shunt voltage to current
 * @param shunt_mv: Shunt voltage in millivolts
 * @retval Current in amperes
 */
static float ConvertToCurrent(float shunt_mv)
{
    // I = V / R, where R = 0.75Ω
    return (shunt_mv / 1000.0f) / INA226_SHUNT_RESISTANCE_OHMS;
}

/**
 * @brief Convert current and voltage to power
 * @param current_a: Current in amperes
 * @param voltage_v: Voltage in volts
 * @retval Power in watts
 */
static float ConvertToPower(float current_a, float voltage_v)
{
    return current_a * voltage_v;
}

/**
 * @brief Example function to demonstrate optimized integration
 * @retval None
 */
void INA226_IntegrationOptimized_Example(void)
{
    logi("=== INA226 Optimized Integration Example ===\r\n");
    
    // Initialize
    if (!INA226_IntegrationOptimized_Init()) {
        logi("Integration initialization failed!\r\n");
        return;
    }
    
    // Start integration
    if (!INA226_IntegrationOptimized_Start()) {
        logi("Integration start failed!\r\n");
        return;
    }
    
    logi("Integration running... (will run in background task)\r\n");
    logi("Use INA226_IntegrationOptimized_Stop() to stop.\r\n");
    
    // In a real application, this would return and let the task run
    // For demo purposes, we'll wait a bit then stop
    s_delay_ms(30000); // Run for 30 seconds
    
    INA226_IntegrationOptimized_Stop();
    logi("Integration example completed.\r\n");
}
