/**
 ******************************************************************************
 * @file    ina226_manual_test.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   Manual test for INA226 EXTI debugging
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "ina226_manual_test.h"
#include "mod_ina226.h"
#include "app_uart.h"
#include "app_util.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
/* Public functions ----------------------------------------------------------*/

/**
 * @brief Very simple manual trigger test
 * @retval None
 */
void INA226_VerySimpleTest(void)
{
    logi("=== Very Simple Manual Test ===\r\n");
    
    // Just initialize and start
    if (INA226_InitOptimized() && INA226_StartOptimizedAcquisition()) {
        logi("Ready! Please manually trigger PB8 (connect to GND briefly)\r\n");
        
        // Wait and check for data every second
        for (int i = 0; i < 30; i++) {
            logi("Second %d: ", i + 1);
            
            if (INA226_IsNewDataAvailable()) {
                uint16_t buffer[5];
                uint32_t count = INA226_GetLatestData(buffer, 5);
                logi("GOT %lu SAMPLES! ", count);
                
                if (count > 0) {
                    int16_t val = (int16_t)buffer[0];
                    float mv = val * 0.0025f;
                    float ma = mv / 0.75f; // Convert to mA
                    logi("First: 0x%04X = %.1fmV = %.1fmA", buffer[0], mv, ma);
                }
                logi("\r\n");
            } else {
                logi("No data\r\n");
            }
            
            s_delay_ms(1000);
        }
        
        INA226_StopOptimizedAcquisition();
    } else {
        logi("ERROR: Init failed\r\n");
    }
}

/**
 * @brief Test with continuous monitoring
 * @retval None
 */
void INA226_ContinuousMonitorTest(void)
{
    logi("=== Continuous Monitor Test ===\r\n");
    
    if (!INA226_InitOptimized()) {
        logi("ERROR: Init failed\r\n");
        return;
    }
    
    if (!INA226_StartOptimizedAcquisition()) {
        logi("ERROR: Start failed\r\n");
        return;
    }
    
    logi("Continuous monitoring started...\r\n");
    logi("This will show data as soon as EXTI interrupts occur\r\n");
    
    uint32_t total_batches = 0;
    uint32_t total_samples = 0;
    
    // Monitor for 60 seconds
    for (int i = 0; i < 600; i++) { // 60 seconds, 100ms intervals
        if (INA226_IsNewDataAvailable()) {
            uint16_t buffer[10];
            uint32_t count = INA226_GetLatestData(buffer, 10);
            total_batches++;
            total_samples += count;
            
            logi("Batch %lu: %lu samples at %d.%d sec\r\n", 
                 total_batches, count, i/10, i%10);
            
            // Show first sample details
            if (count > 0) {
                int16_t signed_val = (int16_t)buffer[0];
                float shunt_mv = signed_val * 0.0025f;
                float current_ma = shunt_mv / 0.75f; // mA
                float power_mw = current_ma * 3.3f; // Assume 3.3V
                
                logi("  Sample 1: 0x%04X -> %.2fmV -> %.2fmA -> %.1fmW\r\n",
                     buffer[0], shunt_mv, current_ma, power_mw);
            }
        }
        
        s_delay_ms(100);
    }
    
    logi("Continuous test completed:\r\n");
    logi("  Total batches: %lu\r\n", total_batches);
    logi("  Total samples: %lu\r\n", total_samples);
    logi("  Average rate: %.1f samples/sec\r\n", (float)total_samples / 60.0f);
    
    INA226_StopOptimizedAcquisition();
}

/**
 * @brief Test raw register reading
 * @retval None
 */
void INA226_RawRegisterTest(void)
{
    uint16_t reg_value;
    
    logi("=== Raw Register Test ===\r\n");
    
    if (!INA226_Init()) {
        logi("ERROR: Basic init failed\r\n");
        return;
    }
    
    logi("Reading INA226 registers continuously...\r\n");
    
    for (int i = 0; i < 20; i++) {
        logi("Read %d: ", i + 1);
        
        if (INA226_ReadRegister(0x01, &reg_value)) { // Shunt voltage
            int16_t signed_val = (int16_t)reg_value;
            float shunt_mv = signed_val * 0.0025f;
            float current_ma = shunt_mv / 0.75f;
            
            logi("Shunt=0x%04X (%.2fmV, %.2fmA) ", reg_value, shunt_mv, current_ma);
        }
        
        if (INA226_ReadRegister(0x02, &reg_value)) { // Bus voltage
            float bus_v = reg_value * 0.00125f;
            logi("Bus=0x%04X (%.2fV) ", reg_value, bus_v);
        }
        
        if (INA226_ReadRegister(0x06, &reg_value)) { // Mask/Enable
            logi("Mask=0x%04X ", reg_value);
            if (reg_value & 0x0008) logi("CVRF ");
            if (reg_value & 0x0010) logi("AFF ");
        }
        
        logi("\r\n");
        s_delay_ms(500);
    }
}

/**
 * @brief Run basic manual tests
 * @retval None
 */
void INA226_RunManualTests(void)
{
    logi("=== INA226 Manual Tests ===\r\n");
    
    // Test 1: Raw register reading
    INA226_RawRegisterTest();
    s_delay_ms(2000);
    
    // Test 2: Very simple test
    INA226_VerySimpleTest();
    s_delay_ms(2000);
    
    // Test 3: Continuous monitoring
    INA226_ContinuousMonitorTest();
    
    logi("=== Manual Tests Completed ===\r\n");
}
