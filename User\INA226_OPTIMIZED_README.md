# INA226 优化方案：EXTI+DMA 高速数据采集

## 概述

本优化方案将INA226的数据读取从原来的定时器+队列方式改为EXTI+DMA触发方式，显著提高了数据采集的响应速度和效率。

## 优化前后对比

### 原方案（定时器+队列）
- **触发方式**: TIM3定时器中断（100Hz，10ms间隔）
- **数据读取**: 任务中阻塞式I2C读取
- **响应延迟**: 10ms + I2C传输时间
- **CPU占用**: 较高（任务切换 + 阻塞等待）
- **采样率**: 固定100Hz

### 优化方案（EXTI+DMA）
- **触发方式**: INA226 ALERT引脚EXTI中断
- **数据读取**: DMA自动传输
- **响应延迟**: 微秒级
- **CPU占用**: 极低（中断处理 + DMA）
- **采样率**: 可达kHz级别

## 硬件连接

### 新增连接
```
INA226 ALERT引脚 -> STM32 PA8 (EXTI8)
```

### 完整连接图
```
INA226          STM32F411
------          ---------
VCC      ->     3.3V
GND      ->     GND
SCL      ->     PB6 (I2C1_SCL)
SDA      ->     PB7 (I2C1_SDA)
ALERT    ->     PA8 (EXTI8)      [新增]
```

## 工作原理

### 1. 初始化阶段
```c
bool INA226_InitOptimized(void)
```
- 初始化基本INA226功能
- 配置PA8为EXTI输入（下降沿触发）
- 配置DMA1_Stream0用于I2C1接收
- 配置INA226的ALERT功能（转换完成中断）
- 初始化ping-pong缓冲区

### 2. 数据采集流程

```mermaid
graph TD
    A[INA226转换完成] --> B[ALERT引脚拉低]
    B --> C[EXTI8中断触发]
    C --> D[启动DMA读取0x01寄存器]
    D --> E[DMA传输完成中断]
    E --> F[数据写入ping-pong缓冲区]
    F --> G[切换缓冲区索引]
    G --> H[设置新数据标志]
```

### 3. Ping-Pong缓冲机制

```c
static uint16_t samples_buf[2][100];  // 双缓冲区
static volatile uint32_t active_idx;  // 当前写入缓冲区索引
static volatile uint32_t wr_pos;     // 写入位置
```

- **缓冲区A**: 正在写入数据
- **缓冲区B**: 可供应用程序读取
- **自动切换**: 当一个缓冲区满时自动切换

## API接口

### 初始化函数
```c
// 初始化优化模式
bool INA226_InitOptimized(void);

// 开始优化采集
bool INA226_StartOptimizedAcquisition(void);

// 停止优化采集
void INA226_StopOptimizedAcquisition(void);
```

### 数据获取函数
```c
// 检查是否有新数据
bool INA226_IsNewDataAvailable(void);

// 获取最新数据
uint32_t INA226_GetLatestData(uint16_t *buffer, uint32_t count);
```

### 中断回调函数
```c
// EXTI中断回调
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin);

// DMA完成回调
void HAL_I2C_MemRxCpltCallback(I2C_HandleTypeDef *hi2c);
```

## 使用示例

### 基本使用
```c
#include "mod_ina226.h"

void example_basic_usage(void)
{
    uint16_t data_buffer[100];
    
    // 初始化优化模式
    if (!INA226_InitOptimized()) {
        return; // 初始化失败
    }
    
    // 开始采集
    INA226_StartOptimizedAcquisition();
    
    while (1) {
        // 检查新数据
        if (INA226_IsNewDataAvailable()) {
            uint32_t count = INA226_GetLatestData(data_buffer, 100);
            
            // 处理数据
            for (uint32_t i = 0; i < count; i++) {
                float shunt_mv = (int16_t)data_buffer[i] * 0.0025f;
                float current_a = (shunt_mv / 1000.0f) / 0.75f;
                // 使用电流数据...
            }
        }
        
        // 其他任务...
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}
```

### 高频监控示例
```c
void example_high_frequency_monitoring(void)
{
    // 参见 ina226_optimized_example.c 中的详细实现
    INA226_OptimizedExample_HighFrequency();
}
```

## 性能优势

### 1. 响应速度提升
- **原方案**: 最大10ms延迟
- **优化方案**: 微秒级响应

### 2. CPU占用降低
- **原方案**: 任务切换 + I2C阻塞等待
- **优化方案**: 仅中断处理，DMA自动传输

### 3. 采样率提升
- **原方案**: 固定100Hz
- **优化方案**: 可达kHz级别（取决于INA226配置）

### 4. 功耗优化
- 减少CPU活动时间
- DMA传输期间CPU可进入低功耗模式

## 配置选项

### INA226转换时间配置
```c
// 快速转换（140µs）- 最高采样率
config.bus_conv_time = INA226_CONFIG_VBUSCT_140US;
config.shunt_conv_time = INA226_CONFIG_VSHCT_140US;

// 高精度转换（8.244ms）- 最高精度
config.bus_conv_time = INA226_CONFIG_VBUSCT_8244US;
config.shunt_conv_time = INA226_CONFIG_VSHCT_8244US;
```

### 平均模式配置
```c
// 无平均 - 最高速度
config.avg_mode = INA226_CONFIG_AVG_1;

// 1024次平均 - 最高精度
config.avg_mode = INA226_CONFIG_AVG_1024;
```

## 注意事项

### 1. 中断优先级
- EXTI中断优先级应高于应用任务
- DMA中断优先级应适中

### 2. 缓冲区管理
- 及时读取数据避免缓冲区溢出
- 监控`flag_half_full`标志

### 3. 电源噪声
- 高频采样对电源噪声敏感
- 建议添加适当的滤波电路

### 4. I2C总线速度
- 建议使用400kHz I2C时钟
- 确保I2C总线稳定性

## 故障排除

### 1. 无EXTI中断
- 检查PA8引脚连接
- 确认ALERT功能已配置
- 检查中断优先级设置

### 2. DMA传输失败
- 检查DMA配置
- 确认I2C DMA使能
- 检查内存地址对齐

### 3. 数据异常
- 检查ping-pong缓冲区同步
- 确认数据转换公式
- 验证分流电阻值

## 文件结构

```
User/
├── mod_ina226.h                    # INA226模块头文件（已更新）
├── mod_ina226.c                    # INA226模块实现（已更新）
├── ina226_optimized_example.h     # 优化示例头文件
├── ina226_optimized_example.c     # 优化示例实现
├── stm32f4xx_it.c                 # 中断处理（已更新）
└── INA226_OPTIMIZED_README.md     # 本说明文件
```

## 总结

通过EXTI+DMA优化方案，INA226的数据采集性能得到显著提升：

- ✅ 响应速度从10ms提升到微秒级
- ✅ CPU占用大幅降低
- ✅ 支持kHz级高频采样
- ✅ 实现真正的实时功率监控
- ✅ 能够捕获瞬态功率峰值

这种优化特别适合需要高精度、高频率功率监控的应用场景。
