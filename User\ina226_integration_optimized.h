/**
 ******************************************************************************
 * @file    ina226_integration_optimized.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   INA226 optimized integration header file
 ******************************************************************************
 */

#ifndef __INA226_INTEGRATION_OPTIMIZED_H
#define __INA226_INTEGRATION_OPTIMIZED_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/**
 * @brief Initialize optimized INA226 integration
 * @retval true if initialization successful, false otherwise
 */
bool INA226_IntegrationOptimized_Init(void);

/**
 * @brief Start optimized INA226 integration
 * @retval true if start successful, false otherwise
 */
bool INA226_IntegrationOptimized_Start(void);

/**
 * @brief Stop optimized INA226 integration
 * @retval None
 */
void INA226_IntegrationOptimized_Stop(void);

/**
 * @brief Get integration status
 * @retval true if integration is running, false otherwise
 */
bool INA226_IntegrationOptimized_IsRunning(void);

/**
 * @brief Example function to demonstrate optimized integration
 * @retval None
 */
void INA226_IntegrationOptimized_Example(void);

#ifdef __cplusplus
}
#endif

#endif /* __INA226_INTEGRATION_OPTIMIZED_H */
