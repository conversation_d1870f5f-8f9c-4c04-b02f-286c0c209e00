/**
 ******************************************************************************
 * @file    ina226_optimized_example.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   INA226 optimized EXTI+DMA example implementation
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "ina226_optimized_example.h"
#include "mod_ina226.h"
#include "app_uart.h"
#include "app_util.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define SAMPLE_BUFFER_SIZE      100

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static uint16_t sample_buffer[SAMPLE_BUFFER_SIZE];

/* Private function prototypes -----------------------------------------------*/
static void ProcessShuntVoltageData(uint16_t *data, uint32_t count);
static float ConvertShuntVoltageToMillivolts(uint16_t raw_value);
static float ConvertShuntVoltageToCurrent(float shunt_mv);

/* Public functions ----------------------------------------------------------*/

/**
 * @brief Example 1: Basic optimized INA226 usage
 */
void INA226_OptimizedExample_Basic(void)
{
    logi("=== INA226 Optimized Basic Example ===\r\n");
    
    // Initialize INA226 in optimized mode
    if (!INA226_InitOptimized()) {
        logi("ERROR: INA226 optimized initialization failed!\r\n");
        return;
    }
    
    // Start optimized data acquisition
    if (!INA226_StartOptimizedAcquisition()) {
        logi("ERROR: Failed to start optimized acquisition!\r\n");
        return;
    }
    
    logi("Optimized acquisition started. Waiting for data...\r\n");
    
    // Monitor for 30 seconds
    for (int i = 0; i < 30; i++) {
        // Check if new data is available
        if (INA226_IsNewDataAvailable()) {
            uint32_t samples_read = INA226_GetLatestData(sample_buffer, SAMPLE_BUFFER_SIZE);
            
            if (samples_read > 0) {
                logi("Time %02ds: Received %lu samples\r\n", i + 1, samples_read);
                ProcessShuntVoltageData(sample_buffer, samples_read);
            }
        } else {
            logi("Time %02ds: No new data\r\n", i + 1);
        }
        
        s_delay_ms(1000); // Wait 1 second
    }
    
    // Stop acquisition
    INA226_StopOptimizedAcquisition();
    logi("Basic optimized example completed\r\n");
}

/**
 * @brief Example 2: High-frequency monitoring with statistics
 */
void INA226_OptimizedExample_HighFrequency(void)
{
    uint32_t total_samples = 0;
    uint32_t data_packets = 0;
    float min_current = 999.0f;
    float max_current = -999.0f;
    float avg_current = 0.0f;
    float current_sum = 0.0f;
    
    logi("=== INA226 High-Frequency Monitoring Example ===\r\n");
    
    // Initialize INA226 in optimized mode
    if (!INA226_InitOptimized()) {
        logi("ERROR: INA226 optimized initialization failed!\r\n");
        return;
    }
    
    // Start optimized data acquisition
    if (!INA226_StartOptimizedAcquisition()) {
        logi("ERROR: Failed to start optimized acquisition!\r\n");
        return;
    }
    
    logi("High-frequency monitoring started for 60 seconds...\r\n");
    
    // Monitor for 60 seconds with 100ms checks
    for (int i = 0; i < 600; i++) {
        if (INA226_IsNewDataAvailable()) {
            uint32_t samples_read = INA226_GetLatestData(sample_buffer, SAMPLE_BUFFER_SIZE);
            
            if (samples_read > 0) {
                data_packets++;
                total_samples += samples_read;
                
                // Process each sample
                for (uint32_t j = 0; j < samples_read; j++) {
                    float shunt_mv = ConvertShuntVoltageToMillivolts(sample_buffer[j]);
                    float current_a = ConvertShuntVoltageToCurrent(shunt_mv);
                    
                    // Update statistics
                    if (current_a < min_current) min_current = current_a;
                    if (current_a > max_current) max_current = current_a;
                    current_sum += current_a;
                }
                
                // Print progress every 10 seconds
                if ((i % 100) == 0) {
                    logi("Progress: %d%% - Packets: %lu, Samples: %lu\r\n", 
                         (i * 100) / 600, data_packets, total_samples);
                }
            }
        }
        
        s_delay_ms(100); // Check every 100ms
    }
    
    // Calculate average
    if (total_samples > 0) {
        avg_current = current_sum / total_samples;
    }
    
    // Stop acquisition
    INA226_StopOptimizedAcquisition();
    
    // Print statistics
    logi("\r\n=== Monitoring Statistics ===\r\n");
    logi("Total Data Packets: %lu\r\n", data_packets);
    logi("Total Samples: %lu\r\n", total_samples);
    logi("Average Sampling Rate: %.1f Hz\r\n", (float)total_samples / 60.0f);
    logi("Current Statistics:\r\n");
    logi("  Minimum: %.6f A\r\n", min_current);
    logi("  Maximum: %.6f A\r\n", max_current);
    logi("  Average: %.6f A\r\n", avg_current);
    logi("  Range: %.6f A\r\n", max_current - min_current);
    logi("High-frequency monitoring example completed\r\n");
}

/**
 * @brief Example 3: Power peak detection
 */
void INA226_OptimizedExample_PeakDetection(void)
{
    float peak_threshold = 0.1f; // 100mA threshold
    uint32_t peak_count = 0;
    uint32_t total_samples = 0;
    
    logi("=== INA226 Power Peak Detection Example ===\r\n");
    logi("Peak threshold: %.3f A\r\n", peak_threshold);
    
    // Initialize INA226 in optimized mode
    if (!INA226_InitOptimized()) {
        logi("ERROR: INA226 optimized initialization failed!\r\n");
        return;
    }
    
    // Start optimized data acquisition
    if (!INA226_StartOptimizedAcquisition()) {
        logi("ERROR: Failed to start optimized acquisition!\r\n");
        return;
    }
    
    logi("Peak detection started. Monitoring for 30 seconds...\r\n");
    
    // Monitor for 30 seconds
    for (int i = 0; i < 300; i++) {
        if (INA226_IsNewDataAvailable()) {
            uint32_t samples_read = INA226_GetLatestData(sample_buffer, SAMPLE_BUFFER_SIZE);
            
            if (samples_read > 0) {
                total_samples += samples_read;
                
                // Check each sample for peaks
                for (uint32_t j = 0; j < samples_read; j++) {
                    float shunt_mv = ConvertShuntVoltageToMillivolts(sample_buffer[j]);
                    float current_a = ConvertShuntVoltageToCurrent(shunt_mv);
                    
                    if (current_a > peak_threshold) {
                        peak_count++;
                        logi("PEAK DETECTED: %.6f A at sample %lu\r\n", current_a, total_samples - samples_read + j + 1);
                    }
                }
            }
        }
        
        s_delay_ms(100); // Check every 100ms
    }
    
    // Stop acquisition
    INA226_StopOptimizedAcquisition();
    
    // Print results
    logi("\r\n=== Peak Detection Results ===\r\n");
    logi("Total Samples Analyzed: %lu\r\n", total_samples);
    logi("Peaks Detected: %lu\r\n", peak_count);
    logi("Peak Rate: %.2f%% of samples\r\n", (float)peak_count * 100.0f / total_samples);
    logi("Peak detection example completed\r\n");
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Process shunt voltage data and display statistics
 * @param data: Pointer to raw shunt voltage data
 * @param count: Number of samples
 * @retval None
 */
static void ProcessShuntVoltageData(uint16_t *data, uint32_t count)
{
    if (data == NULL || count == 0) return;
    
    float min_voltage = 999.0f;
    float max_voltage = -999.0f;
    float avg_voltage = 0.0f;
    float voltage_sum = 0.0f;
    
    // Process each sample
    for (uint32_t i = 0; i < count; i++) {
        float voltage_mv = ConvertShuntVoltageToMillivolts(data[i]);
        
        if (voltage_mv < min_voltage) min_voltage = voltage_mv;
        if (voltage_mv > max_voltage) max_voltage = voltage_mv;
        voltage_sum += voltage_mv;
    }
    
    avg_voltage = voltage_sum / count;
    
    // Convert to current
    float min_current = ConvertShuntVoltageToCurrent(min_voltage);
    float max_current = ConvertShuntVoltageToCurrent(max_voltage);
    float avg_current = ConvertShuntVoltageToCurrent(avg_voltage);
    
    logi("  Samples: %lu, Shunt V: %.3f-%.3f mV (avg: %.3f), Current: %.6f-%.6f A (avg: %.6f)\r\n",
         count, min_voltage, max_voltage, avg_voltage, min_current, max_current, avg_current);
}

/**
 * @brief Convert raw shunt voltage value to millivolts
 * @param raw_value: Raw 16-bit value from INA226
 * @retval Shunt voltage in millivolts
 */
static float ConvertShuntVoltageToMillivolts(uint16_t raw_value)
{
    // INA226 shunt voltage LSB is 2.5µV per bit
    // Convert to signed value first
    int16_t signed_value = (int16_t)raw_value;
    return signed_value * 0.0025f; // 2.5µV per bit
}

/**
 * @brief Convert shunt voltage to current using shunt resistance
 * @param shunt_mv: Shunt voltage in millivolts
 * @retval Current in amperes
 */
static float ConvertShuntVoltageToCurrent(float shunt_mv)
{
    // I = V / R, where R = 0.75Ω (750mΩ)
    // Convert mV to V first: shunt_mv / 1000
    return (shunt_mv / 1000.0f) / INA226_SHUNT_RESISTANCE_OHMS;
}
