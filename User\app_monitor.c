/**
 ******************************************************************************
 * @file    app_monitor.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   Module for monitoring INA226 using a hardware timer and FreeRTOS queue.
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "stm32f4xx_tim.h"
#include "stm32f4xx_rcc.h"
#include "misc.h"
#include "app_monitor.h"
#include "app_util.h"
#include "FreeRTOS.h"
#include "queue.h"
#include "task.h"

/* Private define ------------------------------------------------------------*/
#define INA226_DATA_QUEUE_LENGTH    200
#define MONITOR_TIMER_PERIOD_MS     10 // Read data every 10ms (100Hz)

/* Private variables ---------------------------------------------------------*/
static QueueHandle_t ina226_data_queue = NULL;
static TaskHandle_t monitor_task_handle = NULL;

/* Private function prototypes -----------------------------------------------*/
static void Monitor_Timer_Init(void);
static void Monitor_Task(void *pvParameters);

/* Public functions ----------------------------------------------------------*/

/**
 * @brief Initializes the monitoring application module.
 * @retval true if initialization is successful, false otherwise.
 */
bool App_Monitor_Init(void)
{
    // Initialize the INA226 sensor
    if (!INA226_Init()) {
        logi("INA226 initialization failed!\r\n");
        return false;
    }
    logi("INA226 initialized successfully.\r\n");

    // Create a queue to hold the INA226 data
    ina226_data_queue = xQueueCreate(INA226_DATA_QUEUE_LENGTH, sizeof(INA226_Data_t));
    if (ina226_data_queue == NULL) {
        logi("Failed to create INA226 data queue.\r\n");
        return false;
    }
    logi("INA226 data queue created.\r\n");

    // Create the monitoring task
    if (xTaskCreate(Monitor_Task, "MonitorTask", 256, NULL, 2, &monitor_task_handle) != pdPASS) {
        logi("Failed to create monitor task.\r\n");
        return false;
    }
    logi("Monitor task created.\r\n");

    // Initialize the hardware timer for periodic sampling
    Monitor_Timer_Init();
    logi("Monitor timer initialized.\r\n");

    return true;
}

/**
 * @brief Retrieves the latest INA226 data from the queue.
 * @param data Pointer to a structure to store the data.
 * @param timeout_ms Timeout in milliseconds to wait for data.
 * @retval true if data was received, false otherwise.
 */
bool App_Monitor_Get_Data(INA226_Data_t *data, uint32_t timeout_ms)
{
    if (data == NULL || ina226_data_queue == NULL) {
        return false;
    }

    if (xQueueReceive(ina226_data_queue, data, pdMS_TO_TICKS(timeout_ms)) == pdPASS) {
        return true;
    }

    return false;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Initializes TIM3 to generate an interrupt every MONITOR_TIMER_PERIOD_MS.
 */
static void Monitor_Timer_Init(void)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    NVIC_InitTypeDef NVIC_InitStructure;
    RCC_ClocksTypeDef RCC_Clocks;

    // Enable TIM3 clock
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);

    // Get clock frequencies
    RCC_GetClocksFreq(&RCC_Clocks);

    // Calculate timer input clock
    uint32_t timer_clock = RCC_Clocks.PCLK1_Frequency;
    if ((RCC->CFGR & RCC_CFGR_PPRE1) != 0) { // Check APB1 prescaler
        timer_clock *= 2;
    }

    // Configure timer for 10ms interrupt
    // Target frequency is 100Hz (1000ms / 10ms)
    // We want to find a prescaler and period such that:
    // (timer_clock / (prescaler + 1)) / (period + 1) = 100 Hz
    // Let's aim for a 10kHz timer counter clock, which is a good intermediate value.
    uint16_t prescaler = (timer_clock / 10000) - 1; // 10kHz counter clock
    uint16_t period = 1000 - 1; // 10kHz / 100 = 100Hz interrupt

    TIM_TimeBaseStructure.TIM_Period = period;
    TIM_TimeBaseStructure.TIM_Prescaler = prescaler;
    TIM_TimeBaseStructure.TIM_ClockDivision = 0;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM3, &TIM_TimeBaseStructure);

    // Enable TIM3 update interrupt
    TIM_ITConfig(TIM3, TIM_IT_Update, ENABLE);

    // NVIC configuration
    // Note: NVIC priority group is set in main.c as NVIC_PriorityGroup_4
    // This means 4 bits for preemption priority (0-15) and 0 bits for sub priority
    // Priority must be >= configLIBRARY_MAX_SYSCALL_INTERRUPT_PRIORITY (5)
    // to allow calling FreeRTOS API functions from ISR
    NVIC_InitStructure.NVIC_IRQChannel = TIM3_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 6; // Safe priority for FreeRTOS API calls
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0; // Not used in PriorityGroup_4
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // Enable TIM3
    TIM_Cmd(TIM3, ENABLE);
}

/**
 * @brief Monitor task that reads INA226 data and puts it in queue
 */
static void Monitor_Task(void *pvParameters)
{
    INA226_Data_t ina226_data;

    while (1)
    {
        // Wait for notification from timer interrupt (blocking wait)
        ulTaskNotifyTake(pdTRUE, portMAX_DELAY);

        // Read data from the sensor (safe to do in task context)
        if (INA226_ReadData(&ina226_data))
        {
            // Send the data to the queue with a small timeout
            xQueueSend(ina226_data_queue, &ina226_data, pdMS_TO_TICKS(10));
        }
    }
}

/**
 * @brief This function handles TIM3 global interrupt request.
 */
void TIM3_IRQHandler(void)
{
    if (TIM_GetITStatus(TIM3, TIM_IT_Update) != RESET)
    {
        TIM_ClearITPendingBit(TIM3, TIM_IT_Update);

        // Wake up the monitor task to read sensor data
        // This is much safer than doing I2C operations in ISR
        BaseType_t xHigherPriorityTaskWoken = pdFALSE;
        if (monitor_task_handle != NULL)
        {
            vTaskNotifyGiveFromISR(monitor_task_handle, &xHigherPriorityTaskWoken);
            portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
        }
    }
}
