/**
 ******************************************************************************
 * @file    ina226_quick_test.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   Quick test for INA226 ALERT functionality
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "ina226_quick_test.h"
#include "mod_ina226.h"
#include "app_uart.h"
#include "app_util.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
/* Public functions ----------------------------------------------------------*/

/**
 * @brief Quick test to verify EXTI and data flow
 * @retval None
 */
void INA226_QuickTest(void)
{
    logi("=== INA226 Quick Test ===\r\n");
    
    // Step 1: Test data flow
    logi("Step 1: Testing data flow...\r\n");
    INA226_TestDataFlow();
    
    s_delay_ms(2000);
    
    // Step 2: Debug status
    logi("Step 2: Debug status...\r\n");
    INA226_DebugAlertStatus();
    
    logi("Quick test completed.\r\n");
}

/**
 * @brief Simple EXTI test with manual trigger
 * @retval None
 */
void INA226_SimpleEXTITest(void)
{
    logi("=== Simple EXTI Test ===\r\n");
    
    // Initialize optimized mode
    if (!INA226_InitOptimized()) {
        logi("ERROR: Failed to initialize optimized mode\r\n");
        return;
    }
    
    // Start acquisition
    if (!INA226_StartOptimizedAcquisition()) {
        logi("ERROR: Failed to start acquisition\r\n");
        return;
    }
    
    logi("EXTI test started. Manually trigger PB8 or wait for INA226 ALERT...\r\n");
    
    // Monitor for 20 seconds
    for (int i = 0; i < 20; i++) {
        logi("Second %d: ", i + 1);
        
        if (INA226_IsNewDataAvailable()) {
            uint16_t buffer[20];
            uint32_t count = INA226_GetLatestData(buffer, 20);
            logi("Got %lu samples!\r\n", count);
            
            // Show first sample
            if (count > 0) {
                int16_t signed_val = (int16_t)buffer[0];
                float shunt_mv = signed_val * 0.0025f;
                logi("  First sample: 0x%04X (%.3f mV)\r\n", buffer[0], shunt_mv);
            }
        } else {
            logi("No data\r\n");
        }
        
        s_delay_ms(1000);
    }
    
    INA226_StopOptimizedAcquisition();
    logi("Simple EXTI test completed.\r\n");
}

/**
 * @brief Test INA226 conversion rate
 * @retval None
 */
void INA226_ConversionRateTest(void)
{
    uint16_t reg_value;
    
    logi("=== INA226 Conversion Rate Test ===\r\n");
    
    // Initialize basic INA226
    if (!INA226_Init()) {
        logi("ERROR: INA226 init failed\r\n");
        return;
    }
    
    // Read current config
    if (INA226_ReadRegister(0x00, &reg_value)) {
        logi("Current config: 0x%04X\r\n", reg_value);
        
        uint16_t mode = reg_value & 0x0007;
        uint16_t vshct = (reg_value >> 3) & 0x0007;
        uint16_t vbusct = (reg_value >> 6) & 0x0007;
        uint16_t avg = (reg_value >> 9) & 0x0007;
        
        logi("  Mode: 0x%X (%s)\r\n", mode, 
             mode == 7 ? "Continuous" : 
             mode == 3 ? "Triggered" : "Other");
        logi("  VSHCT: 0x%X\r\n", vshct);
        logi("  VBUSCT: 0x%X\r\n", vbusct);
        logi("  AVG: 0x%X\r\n", avg);
    }
    
    // Set to fastest conversion for testing
    uint16_t fast_config = 0x4127; // AVG=1, VBUSCT=140us, VSHCT=140us, Mode=Continuous
    if (INA226_WriteRegister(0x00, fast_config)) {
        logi("Set to fast conversion: 0x%04X\r\n", fast_config);
    }
    
    // Enable CNVR alert
    if (INA226_WriteRegister(0x06, 0x0400)) {
        logi("CNVR alert enabled\r\n");
    }
    
    // Monitor conversion ready flag for 10 seconds
    logi("Monitoring conversion ready for 10 seconds...\r\n");
    uint32_t ready_count = 0;
    
    for (int i = 0; i < 100; i++) { // 10 seconds, 100ms intervals
        if (INA226_ReadRegister(0x06, &reg_value)) {
            if (reg_value & 0x0008) { // CVRF bit
                ready_count++;
                if (ready_count <= 10) {
                    logi("Conversion ready %lu at %d.%d sec\r\n", ready_count, i/10, i%10);
                }
            }
        }
        s_delay_ms(100);
    }
    
    logi("Conversion rate test completed. Ready count: %lu\r\n", ready_count);
    logi("Estimated conversion rate: %.1f Hz\r\n", (float)ready_count / 10.0f);
}

/**
 * @brief Run all quick tests
 * @retval None
 */
void INA226_RunAllQuickTests(void)
{
    logi("=== INA226 All Quick Tests ===\r\n");
    
    // Test 1: Conversion rate
    INA226_ConversionRateTest();
    s_delay_ms(2000);
    
    // Test 2: Simple EXTI
    INA226_SimpleEXTITest();
    s_delay_ms(2000);
    
    // Test 3: Quick test
    INA226_QuickTest();
    
    logi("=== All Quick Tests Completed ===\r\n");
}
