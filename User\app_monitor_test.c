/**
 ******************************************************************************
 * @file    app_monitor_test.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   Test example for optimized app_monitor module
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "app_monitor_test.h"
#include "app_monitor.h"
#include "app_uart.h"
#include "app_util.h"
#include "FreeRTOS.h"
#include "task.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
/* Public functions ----------------------------------------------------------*/

/**
 * @brief Test basic app monitor functionality
 * @retval None
 */
void App_Monitor_Test_Basic(void)
{
    INA226_Data_t monitor_data;
    uint32_t success_count = 0;
    uint32_t timeout_count = 0;
    
    logi("=== App Monitor Basic Test ===\r\n");
    
    // Initialize app monitor
    if (!App_Monitor_Init()) {
        logi("ERROR: App Monitor initialization failed!\r\n");
        return;
    }
    
    logi("App Monitor initialized successfully.\r\n");
    logi("Testing data retrieval for 30 seconds...\r\n");
    
    // Test data retrieval for 30 seconds
    for (int i = 0; i < 30; i++) {
        // Try to get data with 1 second timeout
        if (App_Monitor_Get_Data(&monitor_data, 1000)) {
            success_count++;
            
            logi("Time %02ds: V=%.3fV, I=%.6fA, P=%.3fW, Shunt=%.3fmV\r\n",
                 i + 1,
                 monitor_data.voltage_V,
                 monitor_data.current_A,
                 monitor_data.power_W,
                 monitor_data.shunt_voltage_mV);
        } else {
            timeout_count++;
            logi("Time %02ds: No data (timeout)\r\n", i + 1);
        }
    }
    
    // Print test results
    logi("\r\n=== Test Results ===\r\n");
    logi("Successful reads: %lu\r\n", success_count);
    logi("Timeouts: %lu\r\n", timeout_count);
    logi("Success rate: %.1f%%\r\n", (float)success_count * 100.0f / 30.0f);
    
    if (success_count > 25) {
        logi("Test PASSED: Good data availability\r\n");
    } else {
        logi("Test WARNING: Low data availability\r\n");
    }
    
    logi("Basic test completed.\r\n");
}

/**
 * @brief Test app monitor performance and statistics
 * @retval None
 */
void App_Monitor_Test_Performance(void)
{
    INA226_Data_t monitor_data;
    uint32_t data_count = 0;
    float min_current = 999.0f;
    float max_current = -999.0f;
    float avg_current = 0.0f;
    float current_sum = 0.0f;
    float min_power = 999.0f;
    float max_power = -999.0f;
    float avg_power = 0.0f;
    float power_sum = 0.0f;
    
    logi("=== App Monitor Performance Test ===\r\n");
    
    // Initialize app monitor if not already done
    if (!App_Monitor_IsRunning()) {
        if (!App_Monitor_Init()) {
            logi("ERROR: App Monitor initialization failed!\r\n");
            return;
        }
    }
    
    logi("Collecting performance data for 60 seconds...\r\n");
    
    // Collect data for 60 seconds with 100ms intervals
    for (int i = 0; i < 600; i++) {
        if (App_Monitor_Get_Data(&monitor_data, 100)) {
            data_count++;
            
            // Update current statistics
            if (monitor_data.current_A < min_current) min_current = monitor_data.current_A;
            if (monitor_data.current_A > max_current) max_current = monitor_data.current_A;
            current_sum += monitor_data.current_A;
            
            // Update power statistics
            if (monitor_data.power_W < min_power) min_power = monitor_data.power_W;
            if (monitor_data.power_W > max_power) max_power = monitor_data.power_W;
            power_sum += monitor_data.power_W;
            
            // Print progress every 10 seconds
            if ((i % 100) == 0) {
                logi("Progress: %d%% - Data points: %lu\r\n", (i * 100) / 600, data_count);
            }
        }
        
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    // Calculate averages
    if (data_count > 0) {
        avg_current = current_sum / data_count;
        avg_power = power_sum / data_count;
    }
    
    // Print performance results
    logi("\r\n=== Performance Results ===\r\n");
    logi("Total data points collected: %lu\r\n", data_count);
    logi("Data rate: %.1f samples/second\r\n", (float)data_count / 60.0f);
    logi("\r\nCurrent Statistics:\r\n");
    logi("  Minimum: %.6f A\r\n", min_current);
    logi("  Maximum: %.6f A\r\n", max_current);
    logi("  Average: %.6f A\r\n", avg_current);
    logi("  Range: %.6f A\r\n", max_current - min_current);
    logi("\r\nPower Statistics:\r\n");
    logi("  Minimum: %.6f W\r\n", min_power);
    logi("  Maximum: %.6f W\r\n", max_power);
    logi("  Average: %.6f W\r\n", avg_power);
    logi("  Range: %.6f W\r\n", max_power - min_power);
    
    // Performance evaluation
    if (data_count > 300) {
        logi("\r\nPerformance: EXCELLENT (>5 samples/sec)\r\n");
    } else if (data_count > 120) {
        logi("\r\nPerformance: GOOD (>2 samples/sec)\r\n");
    } else if (data_count > 60) {
        logi("\r\nPerformance: FAIR (>1 sample/sec)\r\n");
    } else {
        logi("\r\nPerformance: POOR (<1 sample/sec)\r\n");
    }
    
    logi("Performance test completed.\r\n");
}

/**
 * @brief Test app monitor start/stop functionality
 * @retval None
 */
void App_Monitor_Test_StartStop(void)
{
    INA226_Data_t monitor_data;
    
    logi("=== App Monitor Start/Stop Test ===\r\n");
    
    // Test 1: Initialize and check status
    logi("Test 1: Initialization\r\n");
    if (!App_Monitor_Init()) {
        logi("ERROR: App Monitor initialization failed!\r\n");
        return;
    }
    
    if (App_Monitor_IsRunning()) {
        logi("PASS: Monitor is running after init\r\n");
    } else {
        logi("FAIL: Monitor is not running after init\r\n");
    }
    
    // Test 2: Get some data while running
    logi("Test 2: Data retrieval while running\r\n");
    bool got_data = false;
    for (int i = 0; i < 10; i++) {
        if (App_Monitor_Get_Data(&monitor_data, 500)) {
            got_data = true;
            logi("PASS: Got data - I=%.6fA, P=%.3fW\r\n", 
                 monitor_data.current_A, monitor_data.power_W);
            break;
        }
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    if (!got_data) {
        logi("FAIL: No data received while running\r\n");
    }
    
    // Test 3: Stop monitor
    logi("Test 3: Stop monitor\r\n");
    App_Monitor_Stop();
    vTaskDelay(pdMS_TO_TICKS(100));
    
    if (!App_Monitor_IsRunning()) {
        logi("PASS: Monitor stopped successfully\r\n");
    } else {
        logi("FAIL: Monitor still running after stop\r\n");
    }
    
    // Test 4: Try to get data while stopped (should timeout quickly)
    logi("Test 4: Data retrieval while stopped\r\n");
    if (App_Monitor_Get_Data(&monitor_data, 500)) {
        logi("WARNING: Got data while stopped (unexpected)\r\n");
    } else {
        logi("PASS: No data while stopped (expected)\r\n");
    }
    
    logi("Start/Stop test completed.\r\n");
}

/**
 * @brief Run all app monitor tests
 * @retval None
 */
void App_Monitor_Test_All(void)
{
    logi("=== App Monitor Complete Test Suite ===\r\n");
    
    // Run basic test
    App_Monitor_Test_Basic();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // Run performance test
    App_Monitor_Test_Performance();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // Run start/stop test
    App_Monitor_Test_StartStop();
    
    logi("=== All Tests Completed ===\r\n");
}
