/**
 ******************************************************************************
 * @file    ina226_quick_test.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   Quick test header for INA226 ALERT functionality
 ******************************************************************************
 */

#ifndef __INA226_QUICK_TEST_H
#define __INA226_QUICK_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/**
 * @brief Quick test to verify EXTI and data flow
 * @retval None
 */
void INA226_QuickTest(void);

/**
 * @brief Simple EXTI test with manual trigger
 * @retval None
 */
void INA226_SimpleEXTITest(void);

/**
 * @brief Test INA226 conversion rate
 * @retval None
 */
void INA226_ConversionRateTest(void);

/**
 * @brief Run all quick tests
 * @retval None
 */
void INA226_RunAllQuickTests(void);

#ifdef __cplusplus
}
#endif

#endif /* __INA226_QUICK_TEST_H */
