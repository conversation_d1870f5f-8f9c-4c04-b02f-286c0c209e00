/**
 ******************************************************************************
 * @file    app_monitor_test.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-03
 * @brief   Test header for optimized app_monitor module
 ******************************************************************************
 */

#ifndef __APP_MONITOR_TEST_H
#define __APP_MONITOR_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/**
 * @brief Test basic app monitor functionality
 * @retval None
 */
void App_Monitor_Test_Basic(void);

/**
 * @brief Test app monitor performance and statistics
 * @retval None
 */
void App_Monitor_Test_Performance(void);

/**
 * @brief Test app monitor start/stop functionality
 * @retval None
 */
void App_Monitor_Test_StartStop(void);

/**
 * @brief Run all app monitor tests
 * @retval None
 */
void App_Monitor_Test_All(void);

#ifdef __cplusplus
}
#endif

#endif /* __APP_MONITOR_TEST_H */
